/* تنسيقات عامة */
body {
  font-family: "<PERSON><PERSON><PERSON>", sans-serif;
  background-color: #ffffff;
}

/* تنسيق البطاقات */
.card {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* تنسيق أزرار الحالة */
.status-buttons .btn {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.status-buttons .btn.active {
  font-weight: bold;
}

/* تنسيق رأس البطاقة */
.card-header {
  font-weight: bold;
}

/* تنسيق شريط التنقل */
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تنسيق النماذج */
.form-control:focus {
  border-color: #212529;
  box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.25);
}

.form-check-input:checked {
  background-color: #212529;
  border-color: #212529;
}
/* تنسيق الأزرار */
.btn {
  border-radius: 4px;
  padding: 0.375rem 1rem;
}

/* تنسيق التنبيهات */
.alert {
  border-radius: 4px;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 768px) {
  .task-card {
    margin-bottom: 1rem;
  }
}

/* تنسيق شريط التنقل المخصص */
.custom-navbar {
    background-color: #ffffff !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.custom-navbar .navbar-brand {
    color: #212529 !important;
}

/* تنسيق عناصر القائمة للمستخدمين المسجلين */
.custom-navbar .navbar-nav .nav-link {
    color: #212529 !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.custom-navbar .navbar-nav .nav-link:hover {
    color: #407BFF !important;
    transform: translateY(-2px);
}

.custom-navbar .navbar-nav .nav-link.active {
    color: #407BFF !important;
    font-weight: 700;
    border-bottom: 2px solid #407BFF;
}

/* تنسيق نص الترحيب */
.custom-navbar .navbar-text {
    color: #212529 !important;
    font-weight: 500;
}

/* تنسيق زر تسجيل الخروج */
.custom-navbar .btn-outline-dark {
    border-color: #212529;
    color: #212529;
    font-weight: 500;
    transition: all 0.3s ease;
}

.custom-navbar .btn-outline-dark:hover {
    background-color: rgba(33, 37, 41, 0.1);
    transform: translateY(-2px);
}

/* تنسيق روابط التنقل للمستخدمين غير المسجلين */
.navbar .d-flex .nav-link {
    color: #212529 !important;
    transition: color 0.3s ease;
    font-weight: 500;
    margin: 0 5px;
}

.navbar .d-flex .nav-link:hover {
    color: #000000;
}

html {
  scroll-behavior: smooth;
}

/* تنسيق لضمان بقاء التذييل في أسفل الصفحة */
html {
    height: 100%;
}

/* body {
    min-height: 100%;
    display: flex;
    flex-direction: column;
} */

.container:not(.footer .container) {
    flex: 1 0 auto;
}

.footer {
    flex-shrink: 0;
    margin-top: auto;
}

/* للصفحات ذات المحتوى القليل مثل dashboard عندما لا توجد مهام */

